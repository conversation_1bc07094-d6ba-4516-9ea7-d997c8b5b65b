"""
Market data service integrating Fyers API with data storage.
"""

from typing import List, Dict, Optional, Any
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
import pandas as pd

from app.core.logging import get_logger
from app.integrations.fyers.client import FyersClient
from app.integrations.fyers.websocket_client import FyersWebSocketClient
from app.integrations.fyers.models import QuoteData, OHLCVData
from app.services.data_service import DataService
from app.database.models import Symbol, MarketType

logger = get_logger(__name__)


class MarketDataService:
    """Service for market data operations with Fyers integration."""
    
    def __init__(self, db: Session):
        """
        Initialize market data service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.data_service = DataService(db)
        self.fyers_client = FyersClient()
        self.websocket_client: Optional[FyersWebSocketClient] = None
        
        # Real-time data storage
        self.live_quotes: Dict[str, QuoteData] = {}
        self.data_callbacks: List[callable] = []
    
    def initialize_fyers_connection(self) -> bool:
        """
        Initialize Fyers API connection.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            success = self.fyers_client.authenticate()
            if success:
                logger.info("Fyers API connection initialized successfully")
                return True
            else:
                logger.error("Failed to initialize Fyers API connection")
                return False
        except Exception as e:
            logger.error(f"Error initializing Fyers connection: {e}")
            return False
    
    def start_real_time_data(self, symbols: List[str]) -> bool:
        """
        Start real-time data streaming for symbols.
        
        Args:
            symbols: List of symbols to stream
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.fyers_client.is_authenticated():
                logger.error("Fyers client not authenticated")
                return False
            
            # Initialize WebSocket client
            self.websocket_client = FyersWebSocketClient(self.fyers_client.access_token)
            
            # Add data callback
            self.websocket_client.add_data_callback(self._on_real_time_data)
            
            # Start WebSocket connection
            if self.websocket_client.start():
                # Subscribe to symbols
                return self.websocket_client.subscribe_symbols(symbols)
            else:
                logger.error("Failed to start WebSocket client")
                return False
                
        except Exception as e:
            logger.error(f"Error starting real-time data: {e}")
            return False
    
    def stop_real_time_data(self) -> None:
        """Stop real-time data streaming."""
        try:
            if self.websocket_client:
                self.websocket_client.stop()
                self.websocket_client = None
            logger.info("Real-time data streaming stopped")
        except Exception as e:
            logger.error(f"Error stopping real-time data: {e}")
    
    def fetch_and_store_historical_data(
        self,
        symbol: str,
        timeframe: str = "1",
        days: int = 30,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> bool:
        """
        Fetch historical data from Fyers and store in database.

        Args:
            symbol: Symbol to fetch data for
            timeframe: Timeframe (1, 5, 15, 30, 60, 1D)
            days: Number of days to fetch (used if start_date/end_date not provided)
            start_date: Custom start date (optional)
            end_date: Custom end date (optional)

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.fyers_client.is_authenticated():
                logger.error("Fyers client not authenticated")
                return False
            
            # Calculate date range
            if start_date and end_date:
                fetch_start_date = start_date
                fetch_end_date = end_date
            else:
                fetch_end_date = datetime.now()
                fetch_start_date = fetch_end_date - timedelta(days=days)
            
            logger.info(f"Fetching historical data for {symbol} ({timeframe}) "
                       f"from {fetch_start_date.date()} to {fetch_end_date.date()}")

            # Fetch data from Fyers
            ohlcv_data = self.fyers_client.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                start_date=fetch_start_date,
                end_date=fetch_end_date
            )
            
            if not ohlcv_data:
                logger.warning(f"No historical data received for {symbol}")
                return False
            
            # Convert to storage format
            storage_data = []
            for ohlcv in ohlcv_data:
                storage_data.append({
                    'timestamp': ohlcv.timestamp,
                    'open': float(ohlcv.open),
                    'high': float(ohlcv.high),
                    'low': float(ohlcv.low),
                    'close': float(ohlcv.close),
                    'volume': int(ohlcv.volume)
                })
            
            # Extract database symbol name from Fyers symbol
            # Convert NSE:NIFTY50-INDEX to NIFTY50, NSE:RELIANCE-EQ to RELIANCE, etc.
            db_symbol_name = self._extract_db_symbol_name(symbol)

            # Store in database using the database symbol name
            success = self.data_service.store_ohlcv_data(db_symbol_name, storage_data)
            
            if success:
                logger.info(f"Successfully stored {len(storage_data)} records for {symbol}")
                return True
            else:
                logger.error(f"Failed to store historical data for {symbol}")
                return False
                
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return False

    def _extract_db_symbol_name(self, fyers_symbol: str) -> str:
        """
        Extract database symbol name from Fyers symbol format.

        Args:
            fyers_symbol: Fyers symbol (e.g., NSE:NIFTY50-INDEX, NSE:RELIANCE-EQ)

        Returns:
            Database symbol name (e.g., NIFTY50, RELIANCE)
        """
        try:
            # Remove exchange prefix
            if ':' in fyers_symbol:
                symbol_part = fyers_symbol.split(':', 1)[1]
            else:
                symbol_part = fyers_symbol

            # Handle different suffixes
            if symbol_part.endswith('-INDEX'):
                return symbol_part[:-6]  # Remove '-INDEX'
            elif symbol_part.endswith('-EQ'):
                return symbol_part[:-3]  # Remove '-EQ'
            else:
                return symbol_part

        except Exception as e:
            logger.warning(f"Could not extract symbol name from {fyers_symbol}: {e}")
            return fyers_symbol
    
    def bulk_fetch_historical_data(
        self,
        symbols: List[str],
        timeframe: str = "1",
        days: int = 30
    ) -> Dict[str, bool]:
        """
        Fetch historical data for multiple symbols.
        
        Args:
            symbols: List of symbols
            timeframe: Timeframe
            days: Number of days
            
        Returns:
            Dictionary with symbol -> success status
        """
        results = {}
        
        for symbol in symbols:
            logger.info(f"Processing {symbol}...")
            results[symbol] = self.fetch_and_store_historical_data(
                symbol, timeframe, days
            )
            
            # Small delay to respect rate limits
            import time
            time.sleep(0.1)
        
        successful = sum(1 for success in results.values() if success)
        logger.info(f"Bulk fetch completed: {successful}/{len(symbols)} symbols successful")
        
        return results
    
    def get_live_quotes(self, symbols: List[str]) -> Dict[str, QuoteData]:
        """
        Get live quotes for symbols.
        
        Args:
            symbols: List of symbols
            
        Returns:
            Dictionary with symbol -> QuoteData
        """
        try:
            if not self.fyers_client.is_authenticated():
                logger.error("Fyers client not authenticated")
                return {}
            
            return self.fyers_client.get_quotes(symbols)
            
        except Exception as e:
            logger.error(f"Error getting live quotes: {e}")
            return {}
    
    def get_cached_quotes(self, symbols: Optional[List[str]] = None) -> Dict[str, QuoteData]:
        """
        Get cached real-time quotes.

        Args:
            symbols: List of symbols (if None, returns all cached quotes)

        Returns:
            Dictionary with symbol -> QuoteData
        """
        if symbols is None:
            return self.live_quotes.copy()

        return {symbol: self.live_quotes[symbol]
                for symbol in symbols if symbol in self.live_quotes}

    def get_live_quote(self, symbol: str) -> Optional[QuoteData]:
        """
        Get live quote for a single symbol.

        Args:
            symbol: Symbol name

        Returns:
            QuoteData if available, None otherwise
        """
        try:
            # First check cached quotes
            if symbol in self.live_quotes:
                return self.live_quotes[symbol]

            # If not cached, try to fetch from Fyers
            if self.fyers_client.is_authenticated():
                quotes = self.fyers_client.get_quotes([symbol])
                if symbol in quotes:
                    # Cache the quote
                    self.live_quotes[symbol] = quotes[symbol]
                    return quotes[symbol]

            return None

        except Exception as e:
            logger.error(f"Error getting live quote for {symbol}: {e}")
            return None
    
    def create_symbol_from_fyers(self, symbol: str, market_type: MarketType) -> Optional[Symbol]:
        """
        Create symbol in database from Fyers data.
        
        Args:
            symbol: Symbol string
            market_type: Market type
            
        Returns:
            Created Symbol object if successful, None otherwise
        """
        try:
            # Get symbol info from Fyers (if available)
            # For now, create with basic information
            symbol_data = {
                'symbol': symbol,
                'name': symbol,  # Can be enhanced with actual name lookup
                'market_type': market_type,
                'exchange': 'NSE',
                'token': symbol,
                'lot_size': 1,
                'tick_size': 0.05,
                'is_active': True
            }
            
            return self.data_service.create_symbol(symbol_data)
            
        except Exception as e:
            logger.error(f"Error creating symbol {symbol}: {e}")
            return None
    
    def add_data_callback(self, callback: callable) -> None:
        """
        Add callback for real-time data updates.
        
        Args:
            callback: Function to call with (symbol, quote_data)
        """
        self.data_callbacks.append(callback)
    
    def _on_real_time_data(self, symbol: str, quote_data: QuoteData) -> None:
        """
        Handle real-time data updates.
        
        Args:
            symbol: Symbol name
            quote_data: Quote data
        """
        try:
            # Cache the quote data
            self.live_quotes[symbol] = quote_data
            
            # Notify callbacks
            for callback in self.data_callbacks:
                try:
                    callback(symbol, quote_data)
                except Exception as e:
                    logger.error(f"Error in data callback: {e}")
            
            # Log periodically (every 100 updates)
            if len(self.live_quotes) % 100 == 0:
                logger.debug(f"Received real-time data for {len(self.live_quotes)} symbols")
                
        except Exception as e:
            logger.error(f"Error processing real-time data for {symbol}: {e}")
    
    def get_market_status(self) -> Dict[str, Any]:
        """
        Get market status information.
        
        Returns:
            Market status dictionary
        """
        try:
            # Get profile to check market status
            profile = self.fyers_client.get_profile()
            
            if profile:
                return {
                    'market_open': True,  # Can be enhanced with actual market hours
                    'timestamp': datetime.now(),
                    'connected_symbols': len(self.live_quotes),
                    'websocket_connected': (
                        self.websocket_client.is_connected 
                        if self.websocket_client else False
                    )
                }
            else:
                return {
                    'market_open': False,
                    'timestamp': datetime.now(),
                    'connected_symbols': 0,
                    'websocket_connected': False
                }
                
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {
                'market_open': False,
                'timestamp': datetime.now(),
                'connected_symbols': 0,
                'websocket_connected': False,
                'error': str(e)
            }
